// worker.js
require("dotenv").config();
const zmq = require("zeromq");
const dbClient = require("../database/index");
const fastJson = require("fast-json-stringify");

// =================== CONFIG ===================
const SERVER_ZR = process.env.SERVER_ZR ?? "tcp://10.22.68.116:2706";
const TOPICS = ["KRX_PUBLISHER_MDDS"];

// =================== INIT ===================
const sub = new zmq.Subscriber();
const stringify = fastJson({ title: "Stock Data", type: "object", additionalProperties: true });
let messageBuffer = [];

// =================== SETUP ===================
sub.connect(SERVER_ZR);
TOPICS.forEach(topic => sub.subscribe(topic));


// database + cache
const oracledb = require('oracledb');
oracledb.autoCommit = true;
const userDB = process.env.USERDB|| 'OTS';
const passwordDB = process.env.PASSWORDDB|| 'ShinhanMay2024';
const connectionString = process.env.CONNECTIONSTRING|| '(DESCRIPTION= (ADDRESS = (PROTOCOL = TCP)(HOST = ***********)(PORT = 1521)) (CONNECT_DATA = (SERVICE_NAME=krx)))';

let configObject = {
    user:userDB,
    password:passwordDB,
    connectString :connectionString,
};

const cache = new Map(); // key: ID, value: dữ liệu cũ
function shouldUpdate(key, newValue) {
  const oldValue = cache.get(key);
  // So sánh: nếu khác thì update cache và return true
  if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
    cache.set(key, newValue); // Cập nhật cache
    return true;
  }
  return false; // Không thay đổi, không cần xử lý
}

// =================== PARSE MESSAGE ===================
async function parseStockData (type, msg, stock_cd, connection) {
    const t = msg;
    switch (type) {        
        case "MD": 
            // Thực hiện lưu vào database
            if (shouldUpdate(stock_cd, t.t270)) {
                let querySQL =`UPDATE OTS.STK01M01 SET CURR_PRI = '${t.t270}' WHERE Brd_Id='G1' AND STK_CD = '${stock_cd}'`;
                let result = await dbClient.executeQuery(connection, querySQL);
            }
            return null
        default:
            return null;
    }
}

// =================== HANDLE ZMQ MESSAGE ===================
(async () => {
    let connection = await oracledb.getConnection(configObject);
    for await (const [topicBuf, messageBuf] of sub) {
        try {
            const raw = JSON.parse(messageBuf);
            const { topic, data } = raw;
            const parts = topic.split("|");
            const dataType = parts[1] ?? "";
            const stockCode = parts[3] ?? "";
            if (parts[2] !== "G1" || !stockCode) continue;
            const result = await parseStockData(dataType, data, stockCode, connection);
            if (!result) continue;
        } catch (err) {
            console.error("[ERROR][ZMQ-SUB]", err);
        }
    }
})();