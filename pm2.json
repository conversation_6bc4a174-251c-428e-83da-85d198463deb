{"apps": [{"name": "socket-streamer", "namespace": "socket-streamer", "script": "./socket/cluster.js", "instances": 1, "exec_mode": "cluster", "log_date_format": "YYYY-MM-DD HH:mm", "merge_logs": true, "max_memory_restart": "4G", "max_restarts": 50, "min_uptime": 5000, "cron_restart": "30 6 * * *", "version": "1.0", "env": {"SERVER_REDIS": "redis://localhost:6379", "REST_API_PORT": 6368, "SERVER_ZR": "tcp://************:2706", "SERVER_STREAMER": "https://online.shinhansec.com.vn"}}]}