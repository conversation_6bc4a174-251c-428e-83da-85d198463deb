require("dotenv").config();
const oracledb = require('oracledb');
oracledb.autoCommit = true;

// database
const userDB = process.env.USERDB|| 'OTS';
const passwordDB = process.env.PASSWORDDB|| 'ShinhanMay2024';
const connectionString = process.env.CONNECTIONSTRING|| '(DESCRIPTION= (ADDRESS = (PROTOCOL = TCP)(HOST = ***********)(PORT = 1521)) (CONNECT_DATA = (SERVICE_NAME=krx)))';

async function executeQuery(connection, sql, binds = {}) {
    try {
        const result = await connection.execute(sql, binds);
        return result;
    } catch (err) {
        console.error(err);
        throw err; // Re-throw for handling in your application
    }
}

async function connectAndRunQuery(querySql, binds = {}, column =[], columnLabel = []) {
    let connection;
    let dataReturn = [];

    try {
        // Connection details (replace with your own)

        let configObject = {
            user:userDB,
            password:passwordDB,
            connectString :connectionString,
        };

        //console.log(configObject);

        //console.log(oracledb.thin ? 'Running in thin mode' : 'Running in thick mode');

        connection = await oracledb.getConnection(configObject);

        console.log('Successfully connected to Oracle Database');

        // Sample query (replace with your desired query)
        const sql = querySql;

        const result = await executeQuery(connection, sql, binds);

        const mappedData = [];

        // console.log('**************>>>******************');
        // console.log(result);
        // console.log('****************<<<<****************');


        for (const row of result.rows) {
            const mappedRow = {};
            const mappedRowColumn = {};

            for (let i = 0; i < result.metaData.length; i++) {
                const meta = result.metaData[i];
                if(column.length == 0) {
                    mappedRow[meta.name.toLowerCase()] = row[i]; // Use column name from metadata
                } else {
                    if(column.includes(meta.name)) {
                        mappedRow[meta.name.toLowerCase()] = row[i];
                    }
                }
            }

            console.log("columnLabel.length "+columnLabel.length);
            console.log("result.metaData.length "+result.metaData.length);

            if(columnLabel.length >0) {

                for (let i = 0; i < columnLabel.length; i++) {
                    const mapColumn = columnLabel[i];
                    mappedRowColumn[mapColumn.toLowerCase()] =  row[i];
                }
                mappedData.push(mappedRowColumn);

            }  else {
                mappedData.push(mappedRow);
            }
        }

        dataReturn = mappedData ;

        console.log('Query results:', dataReturn);

    } catch (err) {
        console.log('Error connecting to Oracle Database:', err);
    } finally {
        if (connection) {
            try {
                // await connection.close();
                console.log('Connection closed');
            } catch (err) {
                console.error('Error closing connection:', err);
            }
        }

        return dataReturn;
    }
}

module.exports ={
    executeQuery,
    connectAndRunQuery
}
